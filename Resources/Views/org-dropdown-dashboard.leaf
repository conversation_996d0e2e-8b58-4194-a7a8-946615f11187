<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Organization Dropdown Manager - Wellup</title>
    <link href="https://fonts.cdnfonts.com/css/graphik" rel="stylesheet">
    <style>
        * {
            margin: 0;
            padding: 0;
            box-sizing: border-box;
        }
        
        body {
            font-family: 'Graphik', sans-serif;
            background: #F5F5F5;
            min-height: 100vh;
            padding: 20px;
        }
        
        .container {
            max-width: 1200px;
            margin: 0 auto;
            background: white;
            border-radius: 12px;
            box-shadow: 0 20px 40px rgba(0, 0, 0, 0.1);
            overflow: hidden;
        }
        
        .header {
            background: linear-gradient(to bottom, #FD8205, #E97100);
            color: white;
            padding: 30px;
            text-align: center;
        }
        
        .header h1 {
            font-size: 32px;
            font-weight: 700;
            margin-bottom: 8px;
        }
        
        .header p {
            font-size: 16px;
            opacity: 0.9;
        }
        
        .content {
            padding: 30px;
        }
        
        .section {
            margin-bottom: 40px;
            border: 1px solid #E5E7EB;
            border-radius: 8px;
            overflow: hidden;
        }
        
        .section-header {
            background: #F9FAFB;
            padding: 20px;
            border-bottom: 1px solid #E5E7EB;
            display: flex;
            justify-content: space-between;
            align-items: center;
        }
        
        .section-title {
            font-size: 20px;
            font-weight: 600;
            color: #111827;
        }
        
        .section-actions {
            display: flex;
            gap: 10px;
        }
        
        .btn {
            padding: 8px 16px;
            border: none;
            border-radius: 6px;
            font-size: 14px;
            font-weight: 500;
            cursor: pointer;
            text-decoration: none;
            display: inline-flex;
            align-items: center;
            gap: 6px;
            transition: all 0.2s;
        }
        
        .btn-primary {
            background: linear-gradient(to bottom, #FD8205, #E97100);
            color: white;
        }
        
        .btn-primary:hover {
            transform: translateY(-1px);
            box-shadow: 0 4px 12px rgba(253, 130, 5, 0.3);
        }
        
        .btn-danger {
            background: #EF4444;
            color: white;
        }
        
        .btn-danger:hover {
            background: #DC2626;
        }
        
        .btn-small {
            padding: 4px 8px;
            font-size: 12px;
        }
        
        .items-table {
            width: 100%;
            border-collapse: collapse;
        }
        
        .items-table th,
        .items-table td {
            padding: 12px 20px;
            text-align: left;
            border-bottom: 1px solid #E5E7EB;
        }
        
        .items-table th {
            background: #F9FAFB;
            font-weight: 600;
            color: #374151;
            font-size: 14px;
        }
        
        .items-table td {
            color: #6B7280;
            font-size: 14px;
        }
        
        .items-table tr:hover {
            background: #F9FAFB;
        }
        
        .color-badge {
            display: inline-block;
            width: 20px;
            height: 20px;
            border-radius: 4px;
            border: 1px solid #E5E7EB;
        }
        
        .empty-state {
            padding: 40px;
            text-align: center;
            color: #6B7280;
        }
        
        .empty-state p {
            font-size: 16px;
            margin-bottom: 20px;
        }
        
        .actions-cell {
            display: flex;
            gap: 8px;
        }
        
        .date-text {
            font-size: 12px;
            color: #9CA3AF;
        }
        
        .modal {
            display: none;
            position: fixed;
            z-index: 1000;
            left: 0;
            top: 0;
            width: 100%;
            height: 100%;
            background-color: rgba(0, 0, 0, 0.5);
        }
        
        .modal-content {
            background-color: white;
            margin: 15% auto;
            padding: 20px;
            border-radius: 8px;
            width: 400px;
            max-width: 90%;
        }
        
        .modal-header {
            display: flex;
            justify-content: space-between;
            align-items: center;
            margin-bottom: 20px;
        }
        
        .modal-title {
            font-size: 18px;
            font-weight: 600;
        }
        
        .close {
            font-size: 24px;
            font-weight: bold;
            cursor: pointer;
            color: #9CA3AF;
        }
        
        .close:hover {
            color: #374151;
        }
    </style>
</head>
<body>
    <div class="container">
        <div class="header">
            <h1>Organization Dropdown Manager</h1>
            <p>Manage dropdown values for your organization</p>
        </div>
        
        <div class="content">
            #for(section in sections):
            <div class="section">
                <div class="section-header">
                    <h2 class="section-title">#(section.displayName)</h2>
                    <div class="section-actions">
                        <a href="/org/#(orgID)/constants/#(section.key)/create" class="btn btn-primary">
                            ➕ Create New Item
                        </a>
                        #if(count(section.items) > 0):
                        <button onclick="confirmRemoveAll('#(section.key)', '#(section.displayName)')" class="btn btn-danger btn-small">
                            🗑️ Remove All
                        </button>
                        #endif
                    </div>
                </div>
                
                #if(count(section.items) > 0):
                <table class="items-table">
                    <thead>
                        <tr>
                            <th>Title</th>
                            <th>Key</th>
                            #if(section.supportsColor):
                            <th>Color</th>
                            #endif
                            <th>Last Updated</th>
                            <th>Actions</th>
                        </tr>
                    </thead>
                    <tbody>
                        #for(item in section.items):
                        <tr>
                            <td><strong>#(item.title)</strong></td>
                            <td><code>#(item.key)</code></td>
                            #if(section.supportsColor):
                            <td>
                                #if(item.color):
                                <span class="color-badge" style="background-color: #(item.color);"></span>
                                #else:
                                <span class="color-badge" style="background-color: #E5E7EB;"></span>
                                #endif
                            </td>
                            #endif
                            <td class="date-text">
                                #if(item.updatedAt):
                                #date(item.updatedAt, "MMM d, yyyy")
                                #else:
                                #date(item.createdAt, "MMM d, yyyy")
                                #endif
                            </td>
                            <td>
                                <div class="actions-cell">
                                    <button onclick="confirmDelete('#(item.key)', '#(item.title)', '#(section.key)')" class="btn btn-danger btn-small">
                                        Delete
                                    </button>
                                </div>
                            </td>
                        </tr>
                        #endfor
                    </tbody>
                </table>
                #else:
                <div class="empty-state">
                    <p>No items in this section yet.</p>
                    <a href="/org/#(orgID)/constants/#(section.key)/create" class="btn btn-primary">
                        Create First Item
                    </a>
                </div>
                #endif
            </div>
            #endfor
        </div>
    </div>
    
    <!-- Confirmation Modals -->
    <div id="deleteModal" class="modal">
        <div class="modal-content">
            <div class="modal-header">
                <h3 class="modal-title">Confirm Delete</h3>
                <span class="close" onclick="closeModal('deleteModal')">&times;</span>
            </div>
            <p>Are you sure you want to delete "<span id="deleteItemName"></span>"?</p>
            <div style="margin-top: 20px; display: flex; gap: 10px; justify-content: flex-end;">
                <button onclick="closeModal('deleteModal')" class="btn" style="background: #E5E7EB; color: #374151;">Cancel</button>
                <form id="deleteForm" method="POST" style="display: inline;">
                    <input type="hidden" name="_method" value="DELETE">
                    <button type="submit" class="btn btn-danger">Delete</button>
                </form>
            </div>
        </div>
    </div>
    
    <div id="removeAllModal" class="modal">
        <div class="modal-content">
            <div class="modal-header">
                <h3 class="modal-title">Confirm Remove All</h3>
                <span class="close" onclick="closeModal('removeAllModal')">&times;</span>
            </div>
            <p>Are you sure you want to remove all items from "<span id="removeAllSectionName"></span>"?</p>
            <div style="margin-top: 20px; display: flex; gap: 10px; justify-content: flex-end;">
                <button onclick="closeModal('removeAllModal')" class="btn" style="background: #E5E7EB; color: #374151;">Cancel</button>
                <form id="removeAllForm" method="POST" style="display: inline;">
                    <button type="submit" class="btn btn-danger">Remove All</button>
                </form>
            </div>
        </div>
    </div>
    
    <script>
        function confirmDelete(itemId, itemName, sectionKey) {
            document.getElementById('deleteItemName').textContent = itemName;
            document.getElementById('deleteForm').action = `/org/#(orgID)/constants/${sectionKey}/${itemId}`;
            document.getElementById('deleteModal').style.display = 'block';
        }
        
        function confirmRemoveAll(sectionKey, sectionName) {
            document.getElementById('removeAllSectionName').textContent = sectionName;
            document.getElementById('removeAllForm').action = `/org/#(orgID)/constants/${sectionKey}/remove-all`;
            document.getElementById('removeAllModal').style.display = 'block';
        }
        
        function closeModal(modalId) {
            document.getElementById(modalId).style.display = 'none';
        }
        
        // Close modal when clicking outside
        window.onclick = function(event) {
            const deleteModal = document.getElementById('deleteModal');
            const removeAllModal = document.getElementById('removeAllModal');
            if (event.target === deleteModal) {
                deleteModal.style.display = 'none';
            }
            if (event.target === removeAllModal) {
                removeAllModal.style.display = 'none';
            }
        }
    </script>
</body>
</html>
