//
//  OrgDropdownController.swift
//  
//
//  Created by <PERSON> on 7/28/25.
//

import Foundation
import Vapor
import Fluent

struct OrgDropdownController: RouteCollection {
    
    func boot(routes: RoutesBuilder) throws {
        let orgRoutes = routes.grouped("org", ":orgID")
        
        // Main dashboard route
        orgRoutes.get(use: dashboard)
        
        // Create item routes
        orgRoutes.get("constants", ":section", "create", use: showCreateForm)
        orgRoutes.post("constants", ":section", "create", use: createItem)
        
        // Remove all items route
        orgRoutes.post("constants", ":section", "remove-all", use: removeAllItems)
        
        // Individual item operations
        orgRoutes.delete("constants", ":section", ":itemID", use: deleteItem)
        orgRoutes.put("constants", ":section", ":itemID", use: updateItem)
    }
    
    // MARK: - Dashboard
    func dashboard(req: Request) throws -> EventLoopFuture<View> {
        guard let orgIDString = req.parameters.get("orgID"),
              let orgID = UUID(orgIDString) else {
            throw Abort(.badRequest, reason: "Invalid organization ID")
        }
        
        // Verify user has access to this organization
        return try verifyOrgAccess(req: req, orgID: orgID).flatMap { _ in
            // Fetch all dropdown items for this organization grouped by section
            return OrgDropdownItem.query(on: req.db)
                .filter(\.$organization.$id == orgID)
                .sort(\.$section)
                .sort(\.$title)
                .all()
                .flatMap { items in
                    // Group items by section
                    let groupedItems = Dictionary(grouping: items) { $0.section }
                    
                    // Create context for the template
                    let context = DashboardContext(
                        orgID: orgIDString,
                        sections: DropdownSection.allCases.map { section in
                            SectionData(
                                key: section.rawValue,
                                displayName: section.displayName,
                                supportsColor: section.supportsColor,
                                items: groupedItems[section.rawValue]?.map { $0.asPublic() } ?? []
                            )
                        }
                    )
                    
                    return req.view.render("org-dropdown-dashboard", context)
                }
        }
    }
    
    // MARK: - Create Item
    func showCreateForm(req: Request) throws -> EventLoopFuture<View> {
        guard let orgIDString = req.parameters.get("orgID"),
              let orgID = UUID(orgIDString),
              let sectionString = req.parameters.get("section"),
              let section = DropdownSection(rawValue: sectionString) else {
            throw Abort(.badRequest, reason: "Invalid parameters")
        }
        
        return try verifyOrgAccess(req: req, orgID: orgID).flatMap { _ in
            let context = CreateItemContext(
                orgID: orgIDString,
                section: section.rawValue,
                sectionDisplayName: section.displayName,
                supportsColor: section.supportsColor
            )
            
            return req.view.render("org-dropdown-create", context)
        }
    }
    
    func createItem(req: Request) throws -> EventLoopFuture<Response> {
        guard let orgIDString = req.parameters.get("orgID"),
              let orgID = UUID(orgIDString),
              let sectionString = req.parameters.get("section"),
              let section = DropdownSection(rawValue: sectionString) else {
            throw Abort(.badRequest, reason: "Invalid parameters")
        }
        
        return try verifyAdminAccess(req: req, orgID: orgID).flatMapThrowing { _ in
            let input = try req.content.decode(CreateDropdownItemInput.self)

            let item = OrgDropdownItem(
                key: input.generateKey(),
                title: input.title,
                color: input.color,
                section: section.rawValue,
                organizationID: orgID
            )

            return item
        }.flatMap { item in
            return item.save(on: req.db).transform(to: req.redirect(to: "/org/\(orgIDString)"))
        }
    }
    
    // MARK: - Remove All Items
    func removeAllItems(req: Request) throws -> EventLoopFuture<Response> {
        guard let orgIDString = req.parameters.get("orgID"),
              let orgID = UUID(orgIDString),
              let sectionString = req.parameters.get("section"),
              let _ = DropdownSection(rawValue: sectionString) else {
            throw Abort(.badRequest, reason: "Invalid parameters")
        }
        
        return try verifyAdminAccess(req: req, orgID: orgID).flatMap { _ in
            return OrgDropdownItem.query(on: req.db)
                .filter(\.$organization.$id == orgID)
                .filter(\.$section == sectionString)
                .delete()
                .transform(to: req.redirect(to: "/org/\(orgIDString)"))
        }
    }
    
    // MARK: - Delete Individual Item
    func deleteItem(req: Request) throws -> EventLoopFuture<Response> {
        guard let orgIDString = req.parameters.get("orgID"),
              let orgID = UUID(orgIDString),
              let itemIDString = req.parameters.get("itemID"),
              let itemID = UUID(itemIDString) else {
            throw Abort(.badRequest, reason: "Invalid parameters")
        }
        
        return try verifyAdminAccess(req: req, orgID: orgID).flatMap { _ in
            return OrgDropdownItem.query(on: req.db)
                .filter(\.$id == itemID)
                .filter(\.$organization.$id == orgID)
                .delete()
                .transform(to: req.redirect(to: "/org/\(orgIDString)"))
        }
    }
    
    // MARK: - Update Item
    func updateItem(req: Request) throws -> EventLoopFuture<Response> {
        guard let orgIDString = req.parameters.get("orgID"),
              let orgID = UUID(orgIDString),
              let itemIDString = req.parameters.get("itemID"),
              let itemID = UUID(itemIDString) else {
            throw Abort(.badRequest, reason: "Invalid parameters")
        }
        
        return try verifyAdminAccess(req: req, orgID: orgID).flatMap { _ in
            do {
                let input = try req.content.decode(UpdateDropdownItemInput.self)

                return OrgDropdownItem.query(on: req.db)
                    .filter(\.$id == itemID)
                    .filter(\.$organization.$id == orgID)
                    .first()
                    .flatMapThrowing { item in
                        guard let item = item else {
                            throw Abort(.notFound, reason: "Item not found")
                        }

                        if let title = input.title {
                            item.title = title
                        }
                        if let color = input.color {
                            item.color = color
                        }
                        if let key = input.key {
                            item.key = key
                        }

                        return item
                    }
                    .flatMap { (item: OrgDropdownItem) in
                        return item.save(on: req.db).transform(to: req.redirect(to: "/org/\(orgIDString)"))
                    }
            } catch {
                return req.eventLoop.future(error: error)
            }
        }
    }

    // MARK: - Helper Methods
    private func verifyOrgAccess(req: Request, orgID: UUID) throws -> EventLoopFuture<Void> {
        return try AuthController.userFromToken(req: req).flatMapThrowing { user in
            // Check if user belongs to this organization
            guard user.$org.id == orgID else {
                throw Abort(.forbidden, reason: "Access denied to this organization")
            }
            return ()
        }
    }

    private func verifyAdminAccess(req: Request, orgID: UUID) throws -> EventLoopFuture<Void> {
        return try AuthController.userFromToken(req: req).flatMapThrowing { user in
            // Check if user belongs to this organization
            guard user.$org.id == orgID else {
                throw Abort(.forbidden, reason: "Access denied to this organization")
            }

            // Check if user has admin role
            guard let roles = user.roles,
                  roles.contains("admin") || roles.contains("administrator") else {
                throw Abort(.forbidden, reason: "Admin access required")
            }

            return ()
        }
    }
}

// MARK: - Context Structs
struct DashboardContext: Content {
    let orgID: String
    let sections: [SectionData]
}

struct SectionData: Content {
    let key: String
    let displayName: String
    let supportsColor: Bool
    let items: [OrgDropdownItem.Public]
}

struct CreateItemContext: Content {
    let orgID: String
    let section: String
    let sectionDisplayName: String
    let supportsColor: Bool
}
