//
//  OrgDropdownItemMigration.swift
//  
//
//  Created by <PERSON> on 7/28/25.
//

import Foundation
import Fluent

struct OrgDropdownItemMigration: Migration {
    func prepare(on database: Database) -> EventLoopFuture<Void> {
        return database.schema("org_dropdown_items")
            .id()
            .field("key", .string, .required)
            .field("title", .string, .required)
            .field("color", .string)
            .field("section", .string, .required)
            .field("organization_id", .uuid, .required, .references("organizations", "id", onDelete: .cascade))
            .field("created_at", .datetime)
            .field("updated_at", .datetime)
            .unique(on: "organization_id", "section", "key", name: "unique_org_section_key")
            .create()
    }
    
    func revert(on database: Database) -> EventLoopFuture<Void> {
        return database.schema("org_dropdown_items").delete()
    }
}
