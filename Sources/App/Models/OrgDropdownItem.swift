//
//  OrgDropdownItem.swift
//  
//
//  Created by <PERSON> on 7/28/25.
//

import Foundation
import Fluent
import Vapor

final class OrgDropdownItem: Model, Content, @unchecked Sendable {
    static let schema = "org_dropdown_items"
    
    @ID var id: UUID?
    
    @Field(key: "key")
    var key: String
    
    @Field(key: "title")
    var title: String
    
    @OptionalField(key: "color")
    var color: String?
    
    @Field(key: "section")
    var section: String
    
    @Parent(key: "organization_id")
    var organization: Organization
    
    @Timestamp(key: "created_at", on: .create)
    var createdAt: Date?
    
    @Timestamp(key: "updated_at", on: .update)
    var updatedAt: Date?
    
    init() { }
    
    init(id: UUID? = nil,
         key: String,
         title: String,
         color: String? = nil,
         section: String,
         organizationID: UUID) {
        self.id = id
        self.key = key
        self.title = title
        self.color = color
        self.section = section
        self.$organization.id = organizationID
    }
}

// MARK: - Content Conformance
extension OrgDropdownItem {
    struct Public: Content {
        let id: UUID?
        let key: String
        let title: String
        let color: String?
        let section: String
        let createdAt: Date?
        let updatedAt: Date?
    }
    
    func asPublic() -> Public {
        return Public(
            id: self.id,
            key: self.key,
            title: self.title,
            color: self.color,
            section: self.section,
            createdAt: self.createdAt,
            updatedAt: self.updatedAt
        )
    }
}

// MARK: - Input Structs
struct CreateDropdownItemInput: Content {
    let title: String
    let color: String?
    let key: String?
    
    func generateKey() -> String {
        if let providedKey = key, !providedKey.isEmpty {
            return providedKey
        }
        return title.lowercased()
            .replacingOccurrences(of: " ", with: "_")
            .replacingOccurrences(of: "[^a-z0-9_]", with: "", options: .regularExpression)
    }
}

struct UpdateDropdownItemInput: Content {
    let title: String?
    let color: String?
    let key: String?
}

// MARK: - Supported Sections
enum DropdownSection: String, CaseIterable {
    case roles = "roles"
    case noteTags = "noteTags"
    case taskTypes = "taskTypes"
    case memberTags = "memberTags"
    case carePackageSections = "carePackageSections"
    case taskCompletionReasons = "taskCompletionReasons"
    
    var displayName: String {
        switch self {
        case .roles:
            return "Roles"
        case .noteTags:
            return "Note Tags"
        case .taskTypes:
            return "Task Types"
        case .memberTags:
            return "Member Tags"
        case .carePackageSections:
            return "Care Package Sections"
        case .taskCompletionReasons:
            return "Task Completion Reasons"
        }
    }
    
    var supportsColor: Bool {
        switch self {
        case .noteTags, .memberTags:
            return true
        default:
            return false
        }
    }
}
